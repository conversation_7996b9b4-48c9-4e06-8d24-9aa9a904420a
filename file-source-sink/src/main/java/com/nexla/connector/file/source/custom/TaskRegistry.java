package com.nexla.connector.file.source.custom;

import com.nexla.connector.file.source.custom.model.Task;
import com.nexla.listing.client.ListingClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

public class TaskRegistry implements AutoCloseable {
    private final static Logger LOGGER = LoggerFactory.getLogger(TaskRegistry.class);
    private final static ScheduledExecutorService SCHEDULED_POOL = Executors.newScheduledThreadPool(1);

    private final ListingClient listingClient;
    private final int sourceId;

    private final List<Task> tasks = new CopyOnWriteArrayList<>();
    private final Map<Long, ScheduledFuture<?>> scheduledFutures = new ConcurrentHashMap<>();
    private final Map<Long, ScheduledFuture<?>> timeoutFutures = new ConcurrentHashMap<>();

    public TaskRegistry(ListingClient listingClient, int sourceId) {
        this.listingClient = listingClient;
        this.sourceId = sourceId;
    }

    public void register(Task task) {
        tasks.add(task);

        if (listingClient == null) {
            return;
        }

        scheduledFutures.put(
                task.getMetadata().getFileId(),
                SCHEDULED_POOL.scheduleAtFixedRate(() -> {
                    listingClient.heartBeat(sourceId, task.getMetadata().getFileId());
                }, 0, 1, TimeUnit.MINUTES)
        );

        timeoutFutures.put(task.getMetadata().getFileId(),
                SCHEDULED_POOL.schedule(() -> {
                    LOGGER.info("Removing task from registry due to timeout: {}", task.getMetadata().getFileId());

                    scheduledFutures.remove(task.getMetadata().getFileId());

                    remove(task.getMetadata().getFileId());
                }, 60, TimeUnit.MINUTES)
        );
    }

    public boolean has(long fileId) {
        return tasks.stream().anyMatch(task -> task.getMetadata().getFileId().equals(fileId));
    }

    public void remove(long fileId) {
        tasks.removeIf(task -> task.getMetadata().getFileId().equals(fileId));

        List<ScheduledFuture<?>> futures = List.of(
                scheduledFutures.remove(fileId),
                timeoutFutures.remove(fileId)
        );

        for (ScheduledFuture<?> future : futures) {
            try {
                if (future != null) {
                    future.cancel(true);
                }
            } catch (Exception e) {
                LOGGER.error("Failed to cancel future: {}", future, e);
            }
        }
    }

    public boolean isEmpty() {
        return tasks.isEmpty();
    }

    public int size() {
        return tasks.size();
    }

    @Override
    public void close() throws Exception {
        SCHEDULED_POOL.shutdown();

        tasks.clear();
    }
}
