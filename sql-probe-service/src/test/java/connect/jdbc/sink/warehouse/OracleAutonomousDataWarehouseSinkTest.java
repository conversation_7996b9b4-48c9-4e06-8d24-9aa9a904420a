package connect.jdbc.sink.warehouse;

import com.google.common.collect.Maps;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Field;
import connect.data.Schema;
import connect.data.SchemaBuilder;
import connect.jdbc.sink.dialect.OracleAutonomousDialect;
import connect.jdbc.sink.dialect.copy.SinkCopyOperationCommon;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyS3Storage;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.testcontainers.shaded.org.apache.commons.lang3.RandomStringUtils;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;
import java.util.stream.Collectors;

import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.INSERT;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class OracleAutonomousDataWarehouseSinkTest {

	@Mock
	private Connection connection;

	@Mock
	private Statement statement;

	@Mock
	private Schema schema;

	@Mock
	private WarehouseCopyS3Storage storage;

	@Test
	void testInsertWriteData() {
		var sink = createSink();
		List<NexlaMessageContext> data = writeMessages(sink);

		assertEquals(2, sink.getBuffer().streamSize());

		var bufferData = sink.getBuffer().getRecords().collect(Collectors.toList());

		assertEquals(data.size(), bufferData.size());

		bufferData.get(0).getNexlaMetaData().getTags().put("originalMessageRunId", 0L);
		bufferData.get(1).getNexlaMetaData().getTags().put("originalMessageRunId", 0L);

		// Compare the entire mapped NexlaMessage objects
		assertEquals(data.get(0).mapped, bufferData.get(0), "First mapped message should be equal");
		assertEquals(data.get(1).mapped, bufferData.get(1), "Second mapped message should be equal");

		sink.getBuffer().close();
	}

	@Test
	void testUpsertWriteData() {
		var configMap = getConfigMap(UPSERT, "id");
		var sink = createSink(WarehouseCopyFileFormat.CSV_GZIP, configMap);

		var nexlaMessage1 = new NexlaMessage(Maps.newLinkedHashMap(Map.of("id", "1", "name", "nexla1")));
		var nexlaMessage2 = new NexlaMessage(Maps.newLinkedHashMap(Map.of("id", "", "name", "nexla2")));
		var nexlaMessage3 = new NexlaMessage(Maps.newLinkedHashMap(Map.of("id", "3", "name", "nexla3")));
		LinkedHashMap<String, Object> map = Maps.newLinkedHashMap();
		map.put("id", null);
		map.put("name", "nexla4");
		var nexlaMessage4 = new NexlaMessage(map);
		var nexlaMessage5 = new NexlaMessage(Maps.newLinkedHashMap(Map.of("id", "5", "name", "nexla3")));
		var topicPartition = new TopicPartition("topic", 1);
		List<NexlaMessageContext> messageList = List.of(
			new NexlaMessageContext(null, nexlaMessage1, topicPartition, 1L),
			new NexlaMessageContext(null, nexlaMessage2, topicPartition, 2L),
			new NexlaMessageContext(null, nexlaMessage3, topicPartition, 3L),
			new NexlaMessageContext(null, nexlaMessage4, topicPartition, 4L),
			new NexlaMessageContext(null, nexlaMessage5, topicPartition, 5L));
		List<NexlaMessageContext> data = writeMessages(sink, messageList);

		var bufferData = sink.getBuffer().getRecords().collect(Collectors.toList());
		bufferData.get(0).getNexlaMetaData().getTags().put("originalMessageRunId", 0L);
		bufferData.get(1).getNexlaMetaData().getTags().put("originalMessageRunId", 0L);
		bufferData.get(2).getNexlaMetaData().getTags().put("originalMessageRunId", 0L);


		assertEquals(3, bufferData.size());
		assertEquals(data.get(0).mapped, bufferData.get(0));
		assertEquals(data.get(2).mapped, bufferData.get(1));
		assertEquals(data.get(4).mapped, bufferData.get(2));

		sink.getBuffer().close();
	}

	@Test
	@SneakyThrows
	void testOnConnectorStop() {
		when(connection.createStatement()).thenReturn(statement);
		when(statement.executeUpdate(anyString())).thenReturn(1);
		when(statement.execute(anyString())).thenReturn(true);

		var sink = createSink();

		sink.onConnectorStop(() -> connection);

		verify(statement).executeUpdate("TRUNCATE TABLE \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\"");
	}

	@Test
	@SneakyThrows
	void testOnConnectorStart() {
		when(connection.createStatement()).thenReturn(statement);
		when(statement.executeUpdate(anyString())).thenReturn(1);
		when(statement.execute(anyString())).thenReturn(true);

		var sink = createSink();

		sink.onConnectorStop(() -> connection);

		verify(statement).executeUpdate("TRUNCATE TABLE \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\"");
	}

	@Test
	@SneakyThrows
	void testFlushBatchInsertCsv() {
		var sink = createSink();
		var queryCaptor = ArgumentCaptor.forClass(String.class);

		when(connection.createStatement()).thenReturn(statement);
		when(statement.execute(anyString())).thenReturn(true);
		when(schema.type()).thenReturn(Schema.Type.STRING);
		when(schema.fields()).thenReturn(List.of(new Field("id", 0, schema), new Field("name", 1, schema)));
		when(storage.getConnectionType()).thenReturn(ConnectionType.S3);
		when(storage.getConfig()).thenReturn(new AWSAuthConfig(getConfigMap(INSERT, "id, name", "{\"mapping\": {\n" +
			"                \"id\": {\n" +
			"                    \"ID\": \"VARCHAR2(3000)\"\n" +
			"                },\n" +
			"                \"name\": {\n" +
			"                    \"NAME\": \"VARCHAR2(3000)\"\n" +
			"                }" +
			"            },\n" +
			"            \"mode\": \"manual\",\n" +
			"            \"tracker_mode\": \"NONE\"}"), 1));
		when(storage.getTempUploadBucket()).thenReturn("nexla");
		when(storage.getUrlPrefix()).thenReturn("s3://");

		writeMessages(sink);

		var result = sink.flushBatch(connection);

		assertTrue(result.isPresent());
		var metricsByRunId = result.get().getMetricsByRunId();
		var recordMetric = metricsByRunId.get(0L);
		var bufferOffsets = result.get().getBufferOffsets();

		assertEquals(2, recordMetric.sentRecordsTotal.get());
		assertEquals(590, recordMetric.sentBytesTotal.get());
		assertEquals(0, recordMetric.errorRecords.get());

		bufferOffsets.forEach((k, v) -> {
			assertEquals("topic", k.topic);
			assertEquals(1, k.partition);
			assertEquals(2L, v);
		});

		verify(statement, times(2)).execute(queryCaptor.capture());

		assertEquals(
				queryCaptor.getAllValues().get(0),
				"{ call DBMS_CLOUD.create_credential (credential_name => 'access_key_id', username => 'access_key_id', password => 'secret_key')}"
		);

		var copyCommand = queryCaptor.getAllValues().get(1);

		assertTrue(copyCommand.contains("{ call DBMS_CLOUD.copy_data(    table_name  => '\"my_table\"',     schema_name => 'my_schema',     credential_name => 'access_key_id',     file_uri_list => 'null',     "));
		assertTrue(copyCommand.contains("field_list => '\"id\" CHAR(10000), \"name\" CHAR(10000)',     format => json_object('ignoremissingcolumns' value 'true', 'quote' value '\"', 'type' value 'csv', 'escape' value 'true', 'delimiter' value ';',  'compression' value 'gzip',  'rejectlimit' value 0, 'dateformat' value 'AUTO', 'timestampformat' value 'AUTO','timestamptzformat' value 'AUTO'))}"));
	}

	@Test
	@SneakyThrows
	void testFlushBatchInsertJson() {
		var sink = createSink(WarehouseCopyFileFormat.JSON, INSERT);
		var queryCaptor = ArgumentCaptor.forClass(String.class);

		when(connection.createStatement()).thenReturn(statement);
		when(statement.execute(anyString())).thenReturn(true);
		when(schema.fields()).thenReturn(List.of(new Field("id", 0, schema), new Field("name", 1, schema)));
		when(storage.getConnectionType()).thenReturn(ConnectionType.S3);
		when(storage.getConfig()).thenReturn(new AWSAuthConfig(getConfigMap(INSERT), 1));
		when(storage.getTempUploadBucket()).thenReturn("nexla");
		when(storage.getUrlPrefix()).thenReturn("s3://");

		writeMessages(sink);

		var result = sink.flushBatch(connection);

		assertTrue(result.isPresent());
		var metricsByRunId = result.get().getMetricsByRunId();
		var recordMetric = metricsByRunId.get(0L);
		var bufferOffsets = result.get().getBufferOffsets();

		assertEquals(2, recordMetric.sentRecordsTotal.get());
		assertEquals(590, recordMetric.sentBytesTotal.get());
		assertEquals(0, recordMetric.errorRecords.get());

		bufferOffsets.forEach((k, v) -> {
			assertEquals("topic", k.topic);
			assertEquals(1, k.partition);
			assertEquals(2L, v);
		});

		verify(statement, times(2)).execute(queryCaptor.capture());

		assertEquals(
				queryCaptor.getAllValues().get(0),
				"{ call DBMS_CLOUD.create_credential (credential_name => 'access_key_id', username => 'access_key_id', password => 'secret_key')}"
		);

		var copyCommand = queryCaptor.getAllValues().get(1);

		assertTrue(copyCommand.contains("{ call DBMS_CLOUD.copy_data(    table_name  => '\"my_table\"',     schema_name => 'my_schema',     credential_name => 'access_key_id',     file_uri_list => 'null',         "));
		assertTrue(copyCommand.contains("format => json_object('type' value 'json', 'rejectlimit' value 0, 'columnpath' value '[\"$.id\",\"$.name\"]'))}"));
	}

	@Test
	@SneakyThrows
	void testFlushBatchUpsertCsv() {
		String tableName = "my_table";
		String tempTableName = "my_table_TEMP_NEXLA_SINK_1";
		var configMap = getConfigMap(UPSERT, "id, name", "{\"mapping\": {\n" +
			"                \"id\": {\n" +
			"                    \"ID\": \"NUMBER(19,0)\"\n" +
			"                },\n" +
			"                \"name\": {\n" +
			"                    \"NAME\": \"VARCHAR2(3000)\"\n" +
			"                }" +
			"            },\n" +
			"            \"mode\": \"manual\",\n" +
			"            \"tracker_mode\": \"NONE\"}");
		var sink = createSink(WarehouseCopyFileFormat.CSV_GZIP, configMap);
		var queryCaptor = ArgumentCaptor.forClass(String.class);
		var resultSetInfSchema = mock(ResultSet.class);
		var resultSetIncId = mock(ResultSet.class);
		var resultSetColumns = mock(ResultSet.class);
		var infSchemaQuery = "SELECT count(*) FROM user_tables WHERE table_name LIKE 'my_table_TEMP_NEXLA_SINK_1' OR table_name LIKE 'my_table_temp_nexla_sink_1' OR table_name LIKE 'MY_TABLE_TEMP_NEXLA_SINK_1' OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY";
		var incrementalIdQuery = "SELECT COALESCE(MAX(\"INCREMENTAL_ID_SINK_1\"), 0) as \"MAX_INCREMENTAL_ID\" FROM \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\"";
		var columnsQuery = String.format("SELECT " +
			"    COLUMN_NAME," +
			"    DATA_TYPE || " +
			"    CASE " +
			"        WHEN DATA_TYPE IN ('CHAR', 'VARCHAR2', 'NCHAR', 'NVARCHAR2') THEN '(' || DATA_LENGTH || ')'" +
			"        WHEN DATA_TYPE IN ('NUMBER', 'FLOAT') AND DATA_PRECISION IS NOT NULL THEN " +
			"            '(' || DATA_PRECISION || " +
			"            CASE WHEN DATA_SCALE IS NOT NULL THEN ',' || DATA_SCALE END || ')'" +
			"        ELSE NULL " +
			"    END AS DATA_TYPE" +
			" FROM " +
			"    USER_TAB_COLUMNS" +
			" WHERE " +
			"    TABLE_NAME = '%s'", tableName);
		when(connection.createStatement()).thenReturn(statement);
		when(statement.execute(anyString())).thenReturn(true);
		when(schema.fields()).thenReturn(List.of(new Field("id", 0, SchemaBuilder.INT64_SCHEMA), new Field("name", 1, SchemaBuilder.STRING_SCHEMA)));
		when(storage.getConnectionType()).thenReturn(ConnectionType.S3);
		when(storage.getTempUploadBucket()).thenReturn("nexla");
		when(storage.getUrlPrefix()).thenReturn("s3://");
		when(resultSetInfSchema.next()).thenReturn(false);
		when(statement.executeQuery(infSchemaQuery)).thenReturn(resultSetInfSchema);
		when(resultSetIncId.next()).thenReturn(true);
		when(resultSetIncId.getLong("MAX_INCREMENTAL_ID")).thenReturn(1L);
		when(statement.executeQuery(incrementalIdQuery)).thenReturn(resultSetIncId);
		when(resultSetColumns.next()).thenReturn(true).thenReturn(true).thenReturn(false);
		when(resultSetColumns.getString("COLUMN_NAME")).thenReturn("ID").thenReturn("NAME");
		when(resultSetColumns.getString("DATA_TYPE")).thenReturn("NUMBER(19,0)").thenReturn("VARCHAR2(3000)");
		when(statement.executeQuery(columnsQuery)).thenReturn(resultSetColumns);
		when(storage.getConfig()).thenReturn(new AWSAuthConfig(getConfigMap(UPSERT), 1));

		writeMessages(sink);

		var result = sink.flushBatch(connection);

		assertFalse(result.isPresent());

		verify(statement, times(3)).execute(queryCaptor.capture());
		verify(statement, times(5)).executeQuery(queryCaptor.capture());
		verify(statement).executeQuery(infSchemaQuery);
		verify(statement).executeQuery(incrementalIdQuery);


		assertEquals(String.format(
				"CREATE TABLE \"my_schema\".\"%s\" (\"ID\" NUMBER(19,0),\"NAME\" VARCHAR2(3000),\"INCREMENTAL_ID_SINK_1\" NUMBER(19,0))", tempTableName),
				queryCaptor.getAllValues().get(0)
		);

		assertEquals(
				"{ call DBMS_CLOUD.create_credential (credential_name => 'access_key_id', username => 'access_key_id', password => 'secret_key')}",
				queryCaptor.getAllValues().get(1)
		);

		var copyCommand = queryCaptor.getAllValues().get(2);

		assertTrue(copyCommand.contains("{ call DBMS_CLOUD.copy_data(    table_name  => '\"my_table_TEMP_NEXLA_SINK_1\"',     schema_name => 'my_schema',     credential_name => 'access_key_id',     file_uri_list => 'null',     "));
		assertTrue(copyCommand.contains("field_list => '\"id\" CHAR(10000), \"name\" CHAR(10000), \"INCREMENTAL_ID_SINK_1\" CHAR(10000)',     format => json_object('ignoremissingcolumns' value 'true', 'quote' value '\"', 'type' value 'csv', 'escape' value 'true', 'delimiter' value ';',  'compression' value 'gzip',  'rejectlimit' value 0, 'dateformat' value 'AUTO', 'timestampformat' value 'AUTO','timestamptzformat' value 'AUTO'))}"));
	}

	@Test
	@SneakyThrows
	void testFlushBatchUpsertJson() {
		var sink = createSink(WarehouseCopyFileFormat.JSON_GZIP, UPSERT);
		var queryCaptor = ArgumentCaptor.forClass(String.class);
		var resultSetInfSchema = mock(ResultSet.class);
		var resultSetIncId = mock(ResultSet.class);
		var infSchemaQuery = "SELECT count(*) FROM user_tables WHERE table_name LIKE 'my_table_TEMP_NEXLA_SINK_1' OR table_name LIKE 'my_table_temp_nexla_sink_1' OR table_name LIKE 'MY_TABLE_TEMP_NEXLA_SINK_1' OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY";
		var incrementalIdQuery = "SELECT COALESCE(MAX(\"INCREMENTAL_ID_SINK_1\"), 0) as \"MAX_INCREMENTAL_ID\" FROM \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\"";
		when(connection.createStatement()).thenReturn(statement);
		when(statement.execute(anyString())).thenReturn(true);
		when(schema.fields()).thenReturn(List.of(new Field("id", 0, schema), new Field("name", 1, schema)));
		when(storage.getConnectionType()).thenReturn(ConnectionType.S3);
		when(storage.getConfig()).thenReturn(new AWSAuthConfig(getConfigMap(UPSERT), 1));
		when(storage.getTempUploadBucket()).thenReturn("nexla");
		when(storage.getUrlPrefix()).thenReturn("s3://");
		when(resultSetInfSchema.next()).thenReturn(true);
		when(resultSetInfSchema.getInt(1)).thenReturn(1);
		when(resultSetIncId.next()).thenReturn(true);
		when(resultSetIncId.getLong("MAX_INCREMENTAL_ID")).thenReturn(1L);
		when(statement.executeQuery(infSchemaQuery)).thenReturn(resultSetInfSchema);
		when(statement.executeQuery(incrementalIdQuery)).thenReturn(resultSetIncId);

		writeMessages(sink);

		var result = sink.flushBatch(connection);

		assertFalse(result.isPresent());

		verify(statement, times(2)).execute(queryCaptor.capture());
		verify(statement, times(4)).executeQuery(queryCaptor.capture());
		verify(statement).executeQuery(infSchemaQuery);
		verify(statement).executeQuery(incrementalIdQuery);

		assertEquals(
				queryCaptor.getAllValues().get(0),
				"{ call DBMS_CLOUD.create_credential (credential_name => 'access_key_id', username => 'access_key_id', password => 'secret_key')}"
		);

		var copyCommand = queryCaptor.getAllValues().get(1);

		assertTrue(copyCommand.contains("{ call DBMS_CLOUD.copy_data(    table_name  => '\"my_table_TEMP_NEXLA_SINK_1\"',     schema_name => 'my_schema',     credential_name => 'access_key_id',     file_uri_list => 'null',         "));
		assertTrue(copyCommand.contains("format => json_object('type' value 'json', 'rejectlimit' value 0, 'columnpath' value '[\"$.id\",\"$.name\",\"$.INCREMENTAL_ID_SINK_1\"]'))}"));
	}

	@SneakyThrows
	@Test
	void testFlushPipeline() {
		var sink = createSink();
		var queryCaptor = ArgumentCaptor.forClass(String.class);
		when(connection.createStatement()).thenReturn(statement);
		when(statement.execute(anyString())).thenReturn(true);

		when(storage.getConfig()).thenReturn(new AWSAuthConfig(getConfigMap(INSERT, "id, name", "{\"mapping\": {\n" +
			"                \"id\": {\n" +
			"                    \"ID\": \"VARCHAR2(3000)\"\n" +
			"                },\n" +
			"                \"name\": {\n" +
			"                    \"NAME\": \"VARCHAR2(3000)\"\n" +
			"                }" +
			"            },\n" +
			"            \"mode\": \"manual\",\n" +
			"            \"tracker_mode\": \"NONE\"}"), 1));

		var schemaResultSet = mock(ResultSet.class);
		when(schemaResultSet.next()).thenReturn(true);
		when(schemaResultSet.getInt(1)).thenReturn(1);
		var infSchemaQuery = "SELECT count(*) FROM user_tables WHERE table_name LIKE 'my_table_TEMP_NEXLA_SINK_1' OR table_name LIKE 'my_table_temp_nexla_sink_1' OR table_name LIKE 'MY_TABLE_TEMP_NEXLA_SINK_1' OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY";
		when(statement.executeQuery(infSchemaQuery)).thenReturn(schemaResultSet);

		var countResultSet = mock(ResultSet.class);
		when(countResultSet.next()).thenReturn(true);
		when(countResultSet.getLong("CC")).thenReturn(2L);
		var countQuery = "SELECT COUNT(1) as CC FROM \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\"";
		when(statement.executeQuery(countQuery)).thenReturn(countResultSet);

		when(schema.fields()).thenReturn(List.of(new Field("id", 0, schema), new Field("name", 1, schema)));

		var errorResultSet = mock(ResultSet.class);
		when(errorResultSet.next()).thenReturn(true).thenReturn(false);
		when(errorResultSet.getInt("ORA_ERR_NUMBER$")).thenReturn(1);
		when(errorResultSet.getString("ORA_ERR_MESG$")).thenReturn("Ora error");
		when(errorResultSet.getObject("id")).thenReturn(UUID.randomUUID());
		when(errorResultSet.getObject("name")).thenReturn(RandomStringUtils.randomAlphabetic(10));
		var getErrorsQuery = String.format("SELECT ORA_ERR_NUMBER$, ORA_ERR_MESG$, %s FROM %s",
			"\"id\",\"name\"",
			"\"my_schema\".\"ERR$_my_table\"");
		when(statement.executeQuery(getErrorsQuery)).thenReturn(errorResultSet);

		var result = sink.flushPipeline(connection);

		assertTrue(result.isPresent());

		var metricsByRunId = result.get().getMetricsByRunId();
		var recordMetric = metricsByRunId.get(0L);
		var bufferOffsets = result.get().getBufferOffsets();

		assertEquals(0, recordMetric.sentRecordsTotal.get());
		assertEquals(0, recordMetric.sentBytesTotal.get());
		assertEquals(1, recordMetric.errorRecords.get());
		assertTrue(bufferOffsets.isEmpty());

		verify(statement, times(4)).executeUpdate(queryCaptor.capture());
		verify(statement, times(7)).execute(queryCaptor.capture());
		verify(statement, times(3)).executeQuery(queryCaptor.capture());

		assertEquals(
				"BEGIN EXECUTE IMMEDIATE 'DELETE FROM \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\" WHERE EXISTS (     SELECT 1     FROM (         SELECT \"id\", \"name\", MAX(\"INCREMENTAL_ID_SINK_1\") AS max_incremental_id         FROM \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\"         GROUP BY \"id\", \"name\"         HAVING COUNT(1) > 1     ) di     WHERE \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\".\"id\" = di.\"id\" AND \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\".\"name\" = di.\"name\"     AND \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\".\"INCREMENTAL_ID_SINK_1\" <> di.max_incremental_id )'; END;",
				queryCaptor.getAllValues().get(0)
		);
		assertEquals(
				"DELETE FROM \"my_schema\".\"my_table\" WHERE (id, name) IN (SELECT id, name FROM \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\")",
				queryCaptor.getAllValues().get(1)
		);
		assertEquals(
			"INSERT INTO \"my_schema\".\"my_table\"(\"id\",\"name\") SELECT \"id\",\"name\" FROM \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\" LOG ERRORS REJECT LIMIT UNLIMITED ",
			queryCaptor.getAllValues().get(2)
		);
		assertEquals(
			"TRUNCATE TABLE \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\"",
			queryCaptor.getAllValues().get(3)
		);
		assertEquals("ALTER SESSION DISABLE PARALLEL DML", queryCaptor.getAllValues().get(4));
		assertEquals("ALTER SESSION DISABLE PARALLEL DDL", queryCaptor.getAllValues().get(5));
		assertEquals("ALTER SESSION DISABLE PARALLEL query", queryCaptor.getAllValues().get(6));

		assertEquals(
			"BEGIN \n" +
				"   EXECUTE IMMEDIATE 'DROP TABLE \"my_schema\".\"ERR$_my_table\"'; \n" +
				"EXCEPTION \n" +
				"   WHEN OTHERS THEN \n" +
				"      IF SQLCODE != -942 THEN \n" +
				"         RAISE; \n" +
				"      END IF; \n" +
				"END;",
			queryCaptor.getAllValues().get(7)
		);

		assertEquals("call dbms_errlog.create_error_log('\"my_schema\".\"my_table\"')", queryCaptor.getAllValues().get(8));

		assertEquals(
			"BEGIN \n" +
				"   EXECUTE IMMEDIATE 'DROP TABLE \"my_schema\".\"ERR$_my_table\"'; \n" +
				"EXCEPTION \n" +
				"   WHEN OTHERS THEN \n" +
				"      IF SQLCODE != -942 THEN \n" +
				"         RAISE; \n" +
				"      END IF; \n" +
				"END;",
			queryCaptor.getAllValues().get(9)
		);

		assertEquals(
				"BEGIN \n" +
						"   EXECUTE IMMEDIATE 'DROP TABLE \"my_schema\".\"my_table_TEMP_NEXLA_SINK_1\"'; \n" +
						"EXCEPTION \n" +
						"   WHEN OTHERS THEN \n" +
						"      IF SQLCODE != -942 THEN \n" +
						"         RAISE; \n" +
						"      END IF; \n" +
						"END;",
				queryCaptor.getAllValues().get(10)
		);

		queryCaptor.getAllValues();
	}

	@SneakyThrows
	@Test
	void testDirectFlushWhenColumnsPassed() {
		var sink = createSink();
		var queryCaptor = ArgumentCaptor.forClass(String.class);
		var files = List.of(new File("file1"), new File("file2"));
		var columns = List.of("id1", "name2");

		when(connection.createStatement()).thenReturn(statement);
		when(statement.executeUpdate(anyString())).thenReturn(1);
		when(storage.getTempUploadBucket()).thenReturn("nexla");
		when(storage.getUrlPrefix()).thenReturn("s3://");

		var result = sink.directFlush(connection, files, columns);

		assertTrue(result.isPresent());
		var metricsByRunId = result.get().getMetricsByRunId();
		var recordMetric = metricsByRunId.get(0L);
		assertEquals(2, recordMetric.sentRecordsTotal.get());

		files.forEach(file -> assertFalse(file.exists()));

		verify(storage, times(2)).uploadFile(any());
		verify(statement, times(2)).executeUpdate(queryCaptor.capture());

		assertEquals("{ call DBMS_CLOUD.copy_data(    table_name  => '\"my_table\"',     schema_name => 'my_schema',     credential_name => 'access_key_id',     file_uri_list => 'null',     field_list => 'id1 CHAR(10000), name2 CHAR(10000)',     format => json_object('ignoremissingcolumns' value 'true', 'quote' value '\"', 'type' value 'csv', 'escape' value 'true', 'delimiter' value ';',  'compression' value 'gzip',  'rejectlimit' value 0, 'dateformat' value 'AUTO', 'timestampformat' value 'AUTO','timestamptzformat' value 'AUTO'))}",
				queryCaptor.getAllValues().get(0));

		assertEquals("{ call DBMS_CLOUD.copy_data(    table_name  => '\"my_table\"',     schema_name => 'my_schema',     credential_name => 'access_key_id',     file_uri_list => 'null',     field_list => 'id1 CHAR(10000), name2 CHAR(10000)',     format => json_object('ignoremissingcolumns' value 'true', 'quote' value '\"', 'type' value 'csv', 'escape' value 'true', 'delimiter' value ';',  'compression' value 'gzip',  'rejectlimit' value 0, 'dateformat' value 'AUTO', 'timestampformat' value 'AUTO','timestamptzformat' value 'AUTO'))}",
				queryCaptor.getAllValues().get(1));
	}

	@SneakyThrows
	@Test
	void testDirectFlushWhenNoColumnsPassed() {
		var sink = createSink();
		var queryCaptor = ArgumentCaptor.forClass(String.class);
		var files = List.of(new File("file1"), new File("file2"));

		when(connection.createStatement()).thenReturn(statement);
		when(statement.executeUpdate(anyString())).thenReturn(1);
		when(storage.getTempUploadBucket()).thenReturn("nexla");
		when(storage.getUrlPrefix()).thenReturn("s3://");
		when(schema.fields()).thenReturn(List.of(new Field("id", 0, schema), new Field("name", 1, schema)));

		var result = sink.directFlush(connection, files, List.of());

		assertTrue(result.isPresent());
		var metricsByRunId = result.get().getMetricsByRunId();
		var recordMetric = metricsByRunId.get(0L);
		assertEquals(2, recordMetric.sentRecordsTotal.get());

		files.forEach(file -> assertFalse(file.exists()));

		verify(storage, times(2)).uploadFile(any());
		verify(statement, times(2)).executeUpdate(queryCaptor.capture());

		assertEquals("{ call DBMS_CLOUD.copy_data(    table_name  => '\"my_table\"',     schema_name => 'my_schema',     credential_name => 'access_key_id',     file_uri_list => 'null',     field_list => '\"id\" CHAR(10000), \"name\" CHAR(10000)',     format => json_object('ignoremissingcolumns' value 'true', 'quote' value '\"', 'type' value 'csv', 'escape' value 'true', 'delimiter' value ';',  'compression' value 'gzip',  'rejectlimit' value 0, 'dateformat' value 'AUTO', 'timestampformat' value 'AUTO','timestamptzformat' value 'AUTO'))}",
				queryCaptor.getAllValues().get(0));

		assertEquals("{ call DBMS_CLOUD.copy_data(    table_name  => '\"my_table\"',     schema_name => 'my_schema',     credential_name => 'access_key_id',     file_uri_list => 'null',     field_list => '\"id\" CHAR(10000), \"name\" CHAR(10000)',     format => json_object('ignoremissingcolumns' value 'true', 'quote' value '\"', 'type' value 'csv', 'escape' value 'true', 'delimiter' value ';',  'compression' value 'gzip',  'rejectlimit' value 0, 'dateformat' value 'AUTO', 'timestampformat' value 'AUTO','timestamptzformat' value 'AUTO'))}",
				queryCaptor.getAllValues().get(1));
	}


	private List<NexlaMessageContext> writeMessages(OracleAutonomousDataWarehouseSink sink) {
		var nexlaMessage = new NexlaMessage(Maps.newLinkedHashMap(Map.of("id", "1", "name", "nexla")));
		var topicPartition = new TopicPartition("topic", 1);
		var data = List.of(
			new NexlaMessageContext(null, nexlaMessage, topicPartition, 1L),
			new NexlaMessageContext(null, nexlaMessage, topicPartition, 2L));
		return writeMessages(sink, data);
	}

	private List<NexlaMessageContext> writeMessages(OracleAutonomousDataWarehouseSink sink,
																									List<NexlaMessageContext> messageList) {
		final Map<Long, Integer> streamSizeByRunId = new HashMap<>();
		messageList.forEach(message -> {
			Long runId = 0L;
			if (message.getOriginal() != null && message.getOriginal().getNexlaMetaData() != null && message.getOriginal().getNexlaMetaData().getRunId() != null) {
				runId = message.getOriginal().getNexlaMetaData().getRunId();
			}
			streamSizeByRunId.compute(runId, (key, value) -> value == null ? 1 : value + 1);
		});
		sink.writeData(messageList, streamSizeByRunId);
		return messageList;
	}

	private OracleAutonomousDataWarehouseSink createSink() {
		return createSink(WarehouseCopyFileFormat.CSV_GZIP, INSERT);
	}


	private OracleAutonomousDataWarehouseSink createSink(WarehouseCopyFileFormat fileFormat, JdbcSinkConnectorConfig.InsertMode insertMode) {
		return createSink(fileFormat, getConfigMap(insertMode));
	}

	private OracleAutonomousDataWarehouseSink createSink(WarehouseCopyFileFormat fileFormat, Map<String, String> configMap) {
		var config = new JdbcSinkConnectorConfig(configMap);
		var common = new SinkCopyOperationCommon(
				config,
				fileFormat,
				schema,
				new OracleAutonomousDialect(),
				mock(NexlaLogger.class)
		);
		return new OracleAutonomousDataWarehouseSink(common, storage);
	}

	private Map<String, String> getConfigMap(JdbcSinkConnectorConfig.InsertMode insertMode) {
		return getConfigMap(insertMode, "id, name");
	}

	private Map<String, String> getConfigMap(JdbcSinkConnectorConfig.InsertMode insertMode, String primaryKey) {
		return getConfigMap(insertMode, primaryKey, null);
	}

	private Map<String, String> getConfigMap(JdbcSinkConnectorConfig.InsertMode insertMode, String primaryKey, String mapping) {
		Map<String, String> config = new HashMap<>(Map.of(
				"sink_id", "1",
				"insert.mode", insertMode.name(),
				"table", "my_table",
				"database_name", "my_db",
				"schema_name", "my_schema",
				"credentials_type", "ORACLE_AUTONOMOUS",
				"unit.test", "true",
				"access_key_id", "access_key_id",
				"secret_key", "secret_key",
				"primary.key", primaryKey
		));
		if (mapping != null) {
			config.put("mapping", mapping);
		}
		return config;
	}
}
