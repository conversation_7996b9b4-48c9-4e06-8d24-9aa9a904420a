package connect.jdbc.sink.dialect.copy;

import com.google.common.collect.Maps;
import com.nexla.common.ConnectionType;
import com.nexla.common.MetricUtils;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.logging.TimeLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connect.common.cdc.DebeziumConstants;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Field;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.copy.filewriter.CsvDataFileWriter;
import connect.jdbc.sink.dialect.copy.filewriter.DataFileWriter;
import connect.jdbc.sink.dialect.copy.filewriter.JsonDataFileWriter;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyTempStorage;
import connect.jdbc.util.JdbcUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.nexla.common.metrics.RecordMetric.quarantineMessage;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Format.CSV_FORMAT;
import static connect.jdbc.sink.dialect.copy.CopyOperationHelper.deleteRowsCommand;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;

/**
 * This class holds common methods and implementations for JDBC sinks that use `copy mode`.
 * It's important to keep this class clean and use it as a composition class in the specific classes' implementation.
 * If any customization is necessary, do not change methods in this class, use it as guidance to implement specific
 * database logic in its own class.
 * <p>
 * Important to mention
 * tempTableName variable is used to control tem table state, if it's null means temp table doesn't exist otherwise it exists
 * INCREMENTAL_ID_COLUMN_NAME is name of the column we add to temp table to avoid duplicates and keep always the latest value for UPSERTs
 * latestProcessedOffsets is needed because for UPSERTs we always reset buffer after flushing batch, so we need to keep track of last processed
 * offsets in order to update it correctly after flushing pipeline
 * For the same reason mentioned above we need totalBytesSent and totalStreamSizeSent, it's an accumulation of every flush batch metrics
 * We never touch destination table, we only delete current IDs and copy data from temp table
 * tempTableName must be reset every pipeline flush or when connector stops
 */
public final class SinkCopyOperationCommon {

	private static final Map<ConnectionType, String> LOCAL_FILE_PREFIX_BY_TYPE = Map.of(
			ConnectionType.S3, "s3_",
			ConnectionType.AZURE_BLB, "azure_",
			ConnectionType.SNOWFLAKE, "direct_");

	private static final String INCREMENTAL_ID_COLUMN_NAME = "INCREMENTAL_ID_SINK_%d";

	private static final String TEMP_TABLE_NAME = "%s_TEMP_NEXLA_SINK_%d";

	@Getter
	private CopyOperationLocalBuffer buffer;

	@Getter
	private final JdbcSinkConnectorConfig config;

	@Getter
	private final NexlaLogger logger;

	@Getter
	private final WarehouseCopyFileFormat fileFormat;

	@Getter
	private Map<TopicPartition, Long> latestProcessedOffsets = Maps.newConcurrentMap();

	private final String incrementalIdColumnName;

	private final DataFileWriter dataFileWriter;

	@Getter
	private final Schema schema;

	@Getter
	private final DbDialect dbDialect;
	@Getter
	private ConcurrentHashMap<Long, Long> totalBytesSentByRunId;


	// Variables used to calculate approximate size of message, and extrapolate that to get total volume
	@Getter
	private AtomicLong totalBytesForApproximation = new AtomicLong(0L);
	@Getter
	private AtomicLong totalMessagesForApproximation = new AtomicLong(0L);

	private final static Long MAX_NUMBER_OF_MESSAGES_FOR_APPROXIMATION = 100L;

	@Getter
	private ConcurrentHashMap<Long, Integer> totalStreamSizeSentByRunId;

	@Getter
	private ConcurrentHashMap<Long, RecordMetric> recordMetricByRunId = new ConcurrentHashMap<>(Map.of(0L, new RecordMetric()));

	@Getter
	private final String tempTableName;

	@Setter
	private Boolean tempTableExists = null;

	private boolean tempTableHasRecords = false;

	@Setter
	private boolean includeSchemaToQualifiedTempTable = false;

	public SinkCopyOperationCommon(JdbcSinkConnectorConfig config,
	                               WarehouseCopyFileFormat fileFormat,
	                               Schema schema,
	                               DbDialect dbDialect,
	                               NexlaLogger logger) {
		this.logger = logger;
		resetBuffer();
		resetMetrics();
		this.config = config;
		this.fileFormat = fileFormat;
		this.schema = schema;
		this.dbDialect = dbDialect;
		this.incrementalIdColumnName = String.format(INCREMENTAL_ID_COLUMN_NAME, config.sinkId);
		this.tempTableName = String.format(TEMP_TABLE_NAME, config.table, config.sinkId);


		if (fileFormat.format == CSV_FORMAT) {
			List<String> extraColumns = new ArrayList<>();
			if (config.insertMode == UPSERT) {
				extraColumns.add(incrementalIdColumnName);

				if (config.cdcEnabled) {
					extraColumns.add(DebeziumConstants.NEXLA_OPERATION_DELETE);
				}
			}
			this.dataFileWriter = new CsvDataFileWriter(fileFormat, schema, dbDialect, logger, extraColumns, config);
		} else {
			this.dataFileWriter = new JsonDataFileWriter(fileFormat);
		}
	}

	public void writeData(List<NexlaMessageContext> records, Map<Long, Integer> streamSizeByRunId) {
		if (UPSERT == config.insertMode) {
			var filteredMessages = StreamEx.of(records)
					.filter(it -> {
						Long runId = 0L;
						if (it.getOriginal() != null && it.getOriginal().getNexlaMetaData() != null && it.getOriginal().getNexlaMetaData().getRunId() != null) {
							runId = it.getOriginal().getNexlaMetaData().getRunId();
						}
						RecordMetric recordMetric = recordMetricByRunId.computeIfAbsent(runId, k -> new RecordMetric());
						if (it.getMapped() != null && it.getMapped().getNexlaMetaData() != null) {
							if (it.getMapped().getNexlaMetaData().getTags() == null) {
								it.getMapped().getNexlaMetaData().setTags(new LinkedHashMap<>());
							}
							it.getMapped().getNexlaMetaData().getTags().put("originalMessageRunId", runId);
						}

						return filterNullPrimaryKey(it.getMapped(), recordMetric);
					});
			var recordsToWrite = dedupByPk(filteredMessages).toList();
			buffer.write(recordsToWrite, records, streamSizeByRunId);
		} else {
			records.forEach(it -> {
				if (it.getMapped() != null && it.getMapped().getNexlaMetaData() != null) {
					Long runId = 0L;
					if (it.getOriginal() != null && it.getOriginal().getNexlaMetaData() != null && it.getOriginal().getNexlaMetaData().getRunId() != null) {
						runId = it.getOriginal().getNexlaMetaData().getRunId();
					}
					if (it.getMapped().getNexlaMetaData().getTags() == null) {
						it.getMapped().getNexlaMetaData().setTags(new LinkedHashMap<>());
					}
					it.getMapped().getNexlaMetaData().getTags().put("originalMessageRunId", runId);
				}
			});
			buffer.write(records, records, streamSizeByRunId);
		}
	}

	public void resetBuffer() {
		if (nonNull(this.buffer) && !this.buffer.isClosed()) {
			this.latestProcessedOffsets.putAll(this.buffer.getOffsets());
			this.buffer.close();
		}
		this.buffer = new CopyOperationLocalBuffer(config);
	}

	public void resetMetrics() {
		totalBytesSentByRunId = new ConcurrentHashMap<>();
		totalStreamSizeSentByRunId = new ConcurrentHashMap<>();
		recordMetricByRunId = new ConcurrentHashMap<>(Map.of(0L, new RecordMetric()));
		
		totalBytesForApproximation = new AtomicLong(0L);
		totalMessagesForApproximation = new AtomicLong(0L);
	}

	public void updateMetrics(long runId, long totalBytesSent, int totalStreamSizeSent) {
		totalBytesSentByRunId.merge(runId, totalBytesSent, Long::sum);
		totalStreamSizeSentByRunId.merge(runId, totalStreamSizeSent, Integer::sum);
		logger.info("M=updateMetrics, runId={}, totalBytesSent={}, totalStreamSizeSent={}", 
			runId, 
			totalBytesSentByRunId.get(runId), 
			totalStreamSizeSentByRunId.get(runId));
	}


	public void addMessageForApproximation(NexlaMessage message) {
		if (totalMessagesForApproximation.get() >= MAX_NUMBER_OF_MESSAGES_FOR_APPROXIMATION) {
			return;
		}
		totalBytesForApproximation.addAndGet(MetricUtils.calcBytes(message.toJsonString()));
		totalMessagesForApproximation.incrementAndGet();
	}

	public Long getAverageMessageSize() {
		if (totalMessagesForApproximation.get() == 0L)
			return 0L;

		return totalBytesForApproximation.get() / totalMessagesForApproximation.get();
	}

	@SneakyThrows
	public File createTempFile(String prefix, String suffix) {
		File localFile = File.createTempFile(prefix, suffix);
		localFile.deleteOnExit();
		return localFile;
	}

	public File writeDataToFile(
			StreamEx<NexlaMessage> records,
			String prefix,
			String suffix) {
		var localFile = createTempFile(prefix, suffix);

		var count = new AtomicInteger(0);

		dataFileWriter.writeDataFile(
				localFile,
				records,
				(exc, nexlaMessage) -> {
					Long runId = Optional.ofNullable(nexlaMessage.getNexlaMetaData())
						.map(NexlaMetaData::getRunId)
						.orElse(0L);
					RecordMetric recordMetric = recordMetricByRunId.computeIfAbsent(runId, k -> new RecordMetric());
					recordMetric.errorRecords.incrementAndGet();
					recordMetric.quarantineMessages.add(quarantineMessage(nexlaMessage, exc.getMessage()));
				},
				it -> count.incrementAndGet());

		logger.info("M=writeDataToFile, totalWrittenRecords={}, totalErrors={}", count.get(), recordMetricByRunId.values().stream().mapToLong(metric -> metric.errorRecords.get()).sum());

		return localFile;
	}

	public String getIncrementalIdColumnName() {
		return dbDialect.q(incrementalIdColumnName);
	}

	public String escapeColumnName(String columnName) {
		return dbDialect.q(columnName);
	}

	public String getColumnsForQuery() {
		return StreamEx.of(schema.fields())
				.map(Field::name)
				.map(dbDialect::q)
				.collect(joining(",", " (", ") "));
	}

	public StreamEx<String> getSchemaFields() {
		return StreamEx.of(schema.fields())
				.map(Field::name)
				.map(dbDialect::q);
	}

	public String getEnrichedColumnsForQuery() {
		var columns = StreamEx.of(schema.fields())
				.map(Field::name)
				.collect(toList());

		columns.add(incrementalIdColumnName);

		if(config.cdcEnabled) {
			columns.add(DebeziumConstants.NEXLA_OPERATION_DELETE.toUpperCase());
		}

		return getEnrichedColumns()
				.stream()
				.collect(joining(",", " (", ") "));
	}

	public List<String> getEnrichedColumns() {
		var columns = StreamEx.of(schema.fields())
				.map(Field::name)
				.collect(toList());

		columns.add(incrementalIdColumnName);

		if(config.cdcEnabled) {
			columns.add(DebeziumConstants.NEXLA_OPERATION_DELETE.toUpperCase());
		}

		return columns
				.stream()
				.map(dbDialect::q)
				.collect(Collectors.toList());
	}

	public String escapeField(String field) {
		return dbDialect.q(field);
	}

	@SneakyThrows
	public boolean isTempTableCreated(Connection connection) {
		// Different databases may use different case
		var sql = String.format(
				"SELECT count(*) FROM information_schema.tables " +
						"WHERE table_name LIKE '%s' OR table_name LIKE '%s' OR table_name LIKE '%s' LIMIT 1",
				tempTableName, tempTableName.toLowerCase(), tempTableName.toUpperCase());
		return isTempTableCreated(connection, sql);
	}

	@SneakyThrows
	public boolean isTempTableCreated(Connection connection, String sql) {
		if (tempTableExists != null) {
			return tempTableExists;
		}

		try (Statement statement = connection.createStatement();
		     ResultSet resultSet = executeQuery(statement, sql)) {
			if (resultSet.next()) {
				this.tempTableExists = resultSet.getInt(1) != 0;
				return this.tempTableExists;
			}
		}
		return false;
	}

	public String getLocalTempFilePrefix(ConnectionType connectionType) {
		return LOCAL_FILE_PREFIX_BY_TYPE.get(connectionType) + config.sinkId;
	}

	public String getStorageFileLocation(String fileName, WarehouseCopyTempStorage storage) {
		var tempUploadPrefix = Optional.ofNullable(storage.getTempUploadPrefix())
				.orElse(String.valueOf(config.sinkId));

		return storage.tempFileLocation(fileName, tempUploadPrefix);
	}

	public List<Object> extractPrimaryKey(List<String> keyFields, Map<String, Object> dataToOutput) {
		return keyFields.stream().map(dataToOutput::get).collect(toList());
	}

	private StreamEx<NexlaMessageContext> dedupByPk(StreamEx<NexlaMessageContext> dataStream) {
		LinkedHashMap<List<Object>, NexlaMessageContext> dedupMap = new LinkedHashMap<>();
		dataStream.forEach(value -> dedupMap.put(extractPrimaryKey(config.primaryKey, value.getMapped().getRawMessage()), value));
		return StreamEx.of(dedupMap.values());
	}

	private boolean filterNullPrimaryKey(NexlaMessage message, RecordMetric recordMetric) {
		List<Object> primaryKeyValues = extractPrimaryKey(config.primaryKey, message.getRawMessage());
		if (primaryKeyValues.contains(null) || primaryKeyValues.contains("")) {
			recordMetric.quarantineMessages.add(RecordMetric.quarantineMessage(message, "Null value in primary key"));
			recordMetric.errorRecords.incrementAndGet();
			return false;
		}
		return true;
	}

	public String getTableName() {
		return config.table;
	}

	public String getQualifiedTableName() {
		return dbDialect.getQualifiedTableName(config.table, config.authConfig.schemaName, JdbcUtils.getDatabaseName(config));
	}

	public String getQualifiedTempTableName() {
		return includeSchemaToQualifiedTempTable
				? dbDialect.getQualifiedTableName(tempTableName, config.authConfig.schemaName, JdbcUtils.getDatabaseName(config))
				: dbDialect.q(tempTableName);
	}

	@SneakyThrows
	public void executeSQL(Statement statement, String sql) {
		logSql(sql);
		statement.execute(sql);
	}

	@SneakyThrows
	public ResultSet executeQuery(Statement statement, String sql) {
		logSql(sql);
		return statement.executeQuery(sql);
	}

	@SneakyThrows
	public int executeUpdate(Statement statement, String sql) {
		logSql(sql);
		var rowUpdated = statement.executeUpdate(sql);
		logUpdated(rowUpdated);
		return rowUpdated;
	}

	public void cleanupTempFiles(File localFile, WarehouseCopyTempStorage storage) {
		deleteLocalFile(localFile);
		storage.deleteFile(localFile.getName());
	}

	public void dropTable(Statement statement, String qualifiedTableName) {
		var dropCommand = "DROP TABLE IF EXISTS " + qualifiedTableName;
		executeSQL(statement, dropCommand);
	}

	public void dropTempTable(Statement statement) {
		var qualifiedTempTableName = getQualifiedTempTableName();
		var dropCommand = "DROP TABLE IF EXISTS " + qualifiedTempTableName;
		dropTempTable(statement, dropCommand);
	}

	public void dropTempTable(Statement statement, String dropCommand) {
		executeSQL(statement, dropCommand);
		tempTableExists = false;
		tempTableHasRecords = false;
	}

	public boolean tempTableHasRecords(Statement statement) throws SQLException {
		if (tempTableHasRecords) {
			return true;
		}
		var columnName = "CC";
		var sql = String.format("SELECT COUNT(1) as %s FROM %s", columnName, getQualifiedTempTableName());
		tempTableHasRecords = executeAndGetSingleResult(statement, columnName, sql) > 0;
		return tempTableHasRecords;
	}

	public long executeAndGetSingleResult(Statement statement, String columnName, String sql) throws SQLException {
		ResultSet resultSet = executeQuery(statement, sql);

		if (!resultSet.next()) {
			throw new IllegalArgumentException("ResultSet empty!");
		}

		return resultSet.getLong(columnName);
	}

	private void addIncrementalIdToRecord(NexlaMessage message, AtomicLong incrementalId) {
		message.getRawMessage().put(incrementalIdColumnName, incrementalId.incrementAndGet());
	}

	public StreamEx<NexlaMessage> prepareRecordsForUpsert(Long currentMaxIncrementalId, Consumer<NexlaMessage> cn) {
		var qualifiedTempTableName = getQualifiedTempTableName();
		var incrementalId = new AtomicLong(currentMaxIncrementalId);
		logger.info("M=prepareRecordsForUpsert, tempTableName={}", qualifiedTempTableName);
		return buffer.getRecords()
				.peek(message -> {
					addIncrementalIdToRecord(message, incrementalId);
					cn.accept(message);
				});
	}

	public <T> Consumer<NexlaMessage> getConsumer(Function<Object, T> fn) {
		return message -> Optional.ofNullable(message.getRawMessage().get(DebeziumConstants.NEXLA_OPERATION_DELETE))
				.ifPresent(v -> {
					if(config.cdcEnabled) {
						message.getRawMessage().put(DebeziumConstants.NEXLA_OPERATION_DELETE, fn.apply(v));
					}
				});
	}

	public File uploadRecordsToStorage(
			StreamEx<NexlaMessage> records,
			WarehouseCopyTempStorage storage) {
		var filePrefix = getLocalTempFilePrefix(storage.getConnectionType());
		var localFile = writeDataToFile(records, filePrefix, fileFormat.extension);
		try (TimeLogger ignored = new TimeLogger(logger, "SinkCopyOperationCommon::upload file (size=" + localFile.length() + ") to storage")) {
			storage.uploadFile(localFile);
			return localFile;
		}
	}

	public void updateRecordMetrics() {
		recordMetricByRunId.forEach((runId, recordMetric) -> {
			Long streamSize = Math.max(totalStreamSizeSentByRunId.getOrDefault(runId, 0) - recordMetric.errorRecords.get(), 0);
			recordMetric.sentBytesTotal.set(streamSize * getAverageMessageSize());
			recordMetric.sentRecordsTotal.set(streamSize);
		});
	}

	public String formatColumnsForQuery(List<String> columns) {
		return columns
				.stream()
				.collect(joining(",", " (", ") "));
	}

	@SneakyThrows
	public void copyDataFromTempToDestinationTable(Statement statement, String cdcParam) {
		setNlsFormat(statement);

		var tempTableName = getQualifiedTempTableName();
		var qualifiedTableName = getQualifiedTableName();

		logger.info("M=copyDataFromTempToDestinationTable, tempTableName={}, qualifiedTableName={}", tempTableName, qualifiedTableName);

		var columns = getColumnsForQuery();
		var cleanColumns = columns.replaceAll("[()]", "").trim();
		var cdcColumn = config.cdcEnabled ? escapeColumnName(DebeziumConstants.NEXLA_OPERATION_DELETE.toUpperCase()) : "";
		var insertCommand = getInsertIntoSelectCommand(tempTableName, qualifiedTableName, cleanColumns, cdcColumn, cdcParam);
		executeUpdate(statement, insertCommand);
	}

	private void setNlsFormat(Statement statement) {
		if (config.authConfig.dbType.equals(ConnectionType.ORACLE) ||
			config.authConfig.dbType.equals(ConnectionType.ORACLE_AUTONOMOUS)) {
			if (config.nlsDateFormat != null) {
				executeUpdate(statement,
					String.format("ALTER SESSION SET NLS_DATE_FORMAT = '%s'", config.nlsDateFormat));
			}
			if (config.nlsTimestampFormat != null) {
				executeUpdate(statement,
					String.format("ALTER SESSION SET NLS_TIMESTAMP_FORMAT = '%s'", config.nlsTimestampFormat));
			}
		}
	}

	private String getInsertIntoSelectCommand(String tempTable, String qualifiedTableName,
																						String columns, String cdcColumn, String cdcParam) {
		boolean isOracleAutonomous = config.authConfig.dbType.equals(ConnectionType.ORACLE_AUTONOMOUS);
		var cdcCondition = StringUtils.isNotBlank(cdcColumn) ? " WHERE " + String.format("%s = %s", cdcColumn, cdcParam) : "";
		return "INSERT INTO " + qualifiedTableName + "(" + columns + ") " +
			"SELECT " + columns +
			" FROM " + tempTable +
			cdcCondition +
			(isOracleAutonomous ? " LOG ERRORS REJECT LIMIT UNLIMITED " : "");
	}

	@SneakyThrows
	public void deleteDataFromDestinationTable(Statement statement) {
		var tempTableName = getQualifiedTempTableName();
		var qualifiedTableName = getQualifiedTableName();

		logger.info("M=deleteDataFromDestinationTable, tempTableName={}, qualifiedTableName={}", tempTableName, qualifiedTableName);

		var deleteCommand = deleteRowsCommand(config.primaryKey,
				dbDialect,
				config.authConfig.schemaName,
				tempTableName,
				qualifiedTableName);

		executeUpdate(statement, deleteCommand);
	}

	@SneakyThrows
	public Long retrieveMaxIncrementalIdFromTempTable(Statement statement) {
		var columnName = "MAX_INCREMENTAL_ID";
		var sql = String.format("SELECT COALESCE(MAX(%s), 0) as %s FROM %s",
				getIncrementalIdColumnName(),
				dbDialect.q(columnName),
				getQualifiedTempTableName());

		return executeAndGetSingleResult(statement, columnName, sql);
	}

	@SneakyThrows
	public void executeTruncate(Statement statement) {
		var sql = String.format("TRUNCATE TABLE  %s", getQualifiedTableName());
		executeUpdate(statement, sql);
	}

	@SneakyThrows
	public void removeDuplicatesFromTempTable(Statement st) {
		var query = "DELETE FROM ${tempTableName} " +
				"USING ( " +
				"SELECT ${primaryKeys}, MAX(${incrementalId}) max_incremental_id " +
				"FROM ${tempTableName} " +
				"GROUP BY ${primaryKeys} " +
				"HAVING COUNT(1) > 1) di " +
				"WHERE ${primaryKeyWhereClause} "  +
				"AND ${tempTableName}.${incrementalId} <> di.max_incremental_id";

		removeDuplicatesFromTempTable(st, query);
	}

	@SneakyThrows
	public void removeDuplicatesFromTempTable(Statement st, String query) {
		var tempTableName = getQualifiedTempTableName();
		logger.info("M=removeDuplicatesFromTempTable, tempTableName={}", tempTableName);

		var primaryKeys = config.primaryKey;

		var primaryKeyWhereClause = primaryKeys
				.stream()
				.map(dbDialect::q)
				.map(it -> String.format("%s.%s = di.%s", tempTableName, it, it))
				.collect(Collectors.joining(" AND "));

		var escapedPrimaryKeys = primaryKeys
				.stream()
				.map(dbDialect::q)
				.collect(joining(", "));

		var params = Map.of("tempTableName", tempTableName,
				"incrementalId", getIncrementalIdColumnName(),
				"primaryKeys", escapedPrimaryKeys,
				"primaryKeyWhereClause", primaryKeyWhereClause
		);

		var sql = StrSubstitutor.replace(query, params);

		executeUpdate(st, sql);
	}

	public void dropTempTable(Supplier<Connection> conn) {
		try (Connection connection = conn.get();
		     Statement statement = connection.createStatement()) {
			dropTempTable(statement);
			if (!connection.getAutoCommit()) {
				connection.commit();
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	private void deleteLocalFile(File localFile) {
		if (localFile != null) {
			localFile.delete();
		}
	}

	public void logSql(String sql) {
		config.ifLogVerbose(() -> logger.info("Executing query={}", sql));
	}

	private void logUpdated(int rowsUpdated) {
		config.ifLogVerbose(() -> logger.info("Rows updated={}", rowsUpdated));
	}
}
