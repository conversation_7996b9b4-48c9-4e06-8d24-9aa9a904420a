package com.nexla.parser;

import org.apache.commons.io.IOUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openpgp.*;
import org.bouncycastle.openpgp.operator.jcajce.*;
import org.bouncycastle.openpgp.operator.bc.BcKeyFingerprintCalculator;
import org.bouncycastle.openpgp.jcajce.JcaPGPObjectFactory;
import org.bouncycastle.bcpg.ArmoredInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Iterator;
import java.util.Optional;

public class PGPEngine implements EncryptEngine {

	private static final Logger log = LoggerFactory.getLogger(PGPEngine.class);

	static {
		if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
			Security.addProvider(new BouncyCastleProvider());
		}
	}

	private String secretKeyRing;
	private String pubKeyRing;
	private String secKeyRingPassword;
	private String userId;
	private String recipientId;

	public void init(String secretKeyRing, String pubKeyRing, String secKeyRingPassword, String userId, String recipientId) {
		this.secretKeyRing = secretKeyRing;
		this.pubKeyRing = pubKeyRing;
		this.secKeyRingPassword = secKeyRingPassword;
		this.userId = userId;
		this.recipientId = recipientId;
	}

	public InputStream readEncrypted(InputStream streamToDecrypt) throws IOException {
		log.info("PGPEngine.readEncrypted - userId: {}, recipientId: {}", userId, recipientId);

		try {
			// Use direct Bouncy Castle implementation (like the working code)
			byte[] decryptedData = decryptPGPDataDirect(streamToDecrypt);
			return new ByteArrayInputStream(decryptedData);
		} catch (Exception e) {
			log.error("Direct Bouncy Castle decryption failed", e);

			// Check if it's the packet type 20 (AEAD) error
			if (e.getMessage() != null && e.getMessage().contains("unknown packet type encountered")) {
				log.warn("Detected packet type 20 (AEAD) error, attempting GPG fallback");
				try {
					return attemptGPGFallback(streamToDecrypt);
				} catch (Exception gpgException) {
					log.error("GPG fallback also failed", gpgException);
					throw new IllegalArgumentException("Both direct Bouncy Castle and GPG fallback failed", e);
				}
			} else {
				throw new IllegalArgumentException(e);
			}
		}
	}

	/**
	 * Direct Bouncy Castle implementation (based on working code)
	 */
	private byte[] decryptPGPDataDirect(InputStream encryptedStream) throws Exception {
		log.info("Using direct Bouncy Castle decryption...");

		// Load the secret key
		PGPSecretKeyRingCollection secretKeyRings = loadSecretKeyRings();

		// Decode the encrypted stream
		InputStream decoderStream = PGPUtil.getDecoderStream(encryptedStream);

		JcaPGPObjectFactory pgpFactory = new JcaPGPObjectFactory(decoderStream);
		Object object = pgpFactory.nextObject();

		// Handle different PGP object types
		PGPEncryptedDataList encryptedDataList;

		if (object instanceof PGPEncryptedDataList) {
			encryptedDataList = (PGPEncryptedDataList) object;
		} else {
			encryptedDataList = (PGPEncryptedDataList) pgpFactory.nextObject();
		}

		// Find the secret key
		PGPPrivateKey privateKey = null;
		PGPPublicKeyEncryptedData encryptedData = null;

		Iterator<PGPEncryptedData> it = encryptedDataList.getEncryptedDataObjects();
		while (privateKey == null && it.hasNext()) {
			encryptedData = (PGPPublicKeyEncryptedData) it.next();
			privateKey = findPrivateKey(secretKeyRings, encryptedData.getKeyID());
		}

		if (privateKey == null) {
			throw new IllegalArgumentException("Secret key for message not found.");
		}

		// Decrypt the data
		InputStream clearStream = encryptedData.getDataStream(
				new JcePublicKeyDataDecryptorFactoryBuilder()
						.setProvider("BC")
						.build(privateKey));

		JcaPGPObjectFactory clearFactory = new JcaPGPObjectFactory(clearStream);
		Object message = clearFactory.nextObject();

		ByteArrayOutputStream output = new ByteArrayOutputStream();

		if (message instanceof PGPCompressedData) {
			PGPCompressedData compressedData = (PGPCompressedData) message;
			JcaPGPObjectFactory pgpFact = new JcaPGPObjectFactory(compressedData.getDataStream());
			message = pgpFact.nextObject();
		}

		if (message instanceof PGPLiteralData) {
			PGPLiteralData literalData = (PGPLiteralData) message;
			InputStream literalStream = literalData.getInputStream();
			byte[] buffer = new byte[8192];
			int bytesRead;
			while ((bytesRead = literalStream.read(buffer)) != -1) {
				output.write(buffer, 0, bytesRead);
			}
		} else if (message instanceof PGPOnePassSignatureList) {
			throw new PGPException("Encrypted message contains a signed message - not literal data.");
		} else {
			throw new PGPException("Message is not a simple encrypted file - type unknown.");
		}

		if (encryptedData.isIntegrityProtected() && !encryptedData.verify()) {
			throw new PGPException("Message failed integrity check");
		}

		return output.toByteArray();
	}

	/**
	 * Load secret key rings from the stored key string
	 */
	private PGPSecretKeyRingCollection loadSecretKeyRings() throws Exception {
		try (InputStream keyIn = new ByteArrayInputStream(secretKeyRing.getBytes(StandardCharsets.UTF_8))) {
			InputStream decoderStream = PGPUtil.getDecoderStream(keyIn);

			// Try to load as armored input first
			if (secretKeyRing.contains("-----BEGIN PGP")) {
				ArmoredInputStream armoredStream = new ArmoredInputStream(decoderStream);
				return new PGPSecretKeyRingCollection(armoredStream, new BcKeyFingerprintCalculator());
			} else {
				return new PGPSecretKeyRingCollection(decoderStream, new BcKeyFingerprintCalculator());
			}
		}
	}

	/**
	 * Find private key by key ID
	 */
	private PGPPrivateKey findPrivateKey(PGPSecretKeyRingCollection keyRings, long keyID) throws Exception {
		PGPSecretKey secretKey = keyRings.getSecretKey(keyID);

		if (secretKey == null) {
			return null;
		}

		return secretKey.extractPrivateKey(
				new JcePBESecretKeyDecryptorBuilder()
						.setProvider("BC")
						.build(secKeyRingPassword.toCharArray()));
	}

	/**
	 * Fallback method using GPG command line for AEAD encrypted files
	 */
	private InputStream attemptGPGFallback(InputStream streamToDecrypt) throws IOException {
		try {
			// Create temporary files
			File tempInput = File.createTempFile("pgp_input_", ".gpg");
			File tempOutput = File.createTempFile("pgp_output_", ".dec");
			File tempKey = File.createTempFile("pgp_key_", ".asc");

			try {
				// Write input stream to temp file
				try (FileOutputStream fos = new FileOutputStream(tempInput)) {
					byte[] buffer = new byte[8192];
					int bytesRead;
					while ((bytesRead = streamToDecrypt.read(buffer)) != -1) {
						fos.write(buffer, 0, bytesRead);
					}
				}

				// Write private key to temp file
				try (FileOutputStream fos = new FileOutputStream(tempKey)) {
					fos.write(secretKeyRing.getBytes(StandardCharsets.UTF_8));
				}

				// Import key and decrypt using GPG
				ProcessBuilder pb = new ProcessBuilder(
						"gpg", "--batch", "--yes", "--pinentry-mode", "loopback",
						"--passphrase", secKeyRingPassword,
						"--import", tempKey.getAbsolutePath());
				Process process = pb.start();
				int importResult = process.waitFor();

				if (importResult != 0) {
					log.warn("GPG key import failed, trying without import");
				}

				// Decrypt the file
				pb = new ProcessBuilder(
						"gpg", "--batch", "--yes", "--pinentry-mode", "loopback",
						"--passphrase", secKeyRingPassword,
						"--output", tempOutput.getAbsolutePath(),
						"--decrypt", tempInput.getAbsolutePath());
				process = pb.start();
				int decryptResult = process.waitFor();

				if (decryptResult != 0) {
					// Read error output
					try (BufferedReader reader = new BufferedReader(
							new InputStreamReader(process.getErrorStream()))) {
						StringBuilder error = new StringBuilder();
						String line;
						while ((line = reader.readLine()) != null) {
							error.append(line).append("\n");
						}
						log.error("GPG decryption failed: {}", error.toString());
					}
					throw new IOException("GPG decryption failed with exit code: " + decryptResult);
				}

				// Return the decrypted content as input stream
				return new FileInputStream(tempOutput) {
					@Override
					public void close() throws IOException {
						super.close();
						// Clean up temp files
						tempInput.delete();
						tempOutput.delete();
						tempKey.delete();
					}
				};

			} catch (Exception e) {
				// Clean up temp files on error
				tempInput.delete();
				tempOutput.delete();
				tempKey.delete();
				throw e;
			}

		} catch (Exception e) {
			log.error("GPG fallback failed", e);
			throw new IOException("GPG fallback failed: " + e.getMessage(), e);
		}
	}

	public ByteArrayOutputStream encrypt(InputStream streamToEncrypt) throws IOException {
		log.info("PGPEngine.encrypt - userId: {}, recipientId: {}", userId, recipientId);
		
		// Check if public key is available for encryption
		if (pubKeyRing == null || pubKeyRing.trim().isEmpty()) {
			throw new IllegalArgumentException("Public key is required for encryption operations. This configuration is for decryption-only mode.");
		}
		
		ByteArrayOutputStream streamToReturn;
		try (
			InputStream secretKey = new ByteArrayInputStream(secretKeyRing.getBytes(StandardCharsets.UTF_8));
			InputStream publicKey = new ByteArrayInputStream(pubKeyRing.getBytes(StandardCharsets.UTF_8))
		) {
			try {
				KeyringConfig encryptionKeyring = KeyringConfigs.withKeyRingsFromStreams(publicKey,
					secretKey, KeyringConfigCallbacks.withPassword(secKeyRingPassword));
				streamToReturn = new ByteArrayOutputStream();
				OutputStream encryptionStream = BouncyGPG
					.encryptToStream()
					.withConfig(encryptionKeyring)
					.withAlgorithms(new PGPAlgorithmSuite(hashAlgorithm, encryptionAlgorithm, compressionAlgorithm))
					.toRecipient(recipientId)
					.andDoNotSign()
					.binaryOutput()
					.andWriteTo(streamToReturn);

				IOUtils.copyLarge(streamToEncrypt, encryptionStream);
				encryptionStream.close();
			} catch (PGPException | IOException | GeneralSecurityException e) {
				log.error("Can't encrypt stream", e);
				throw new IllegalArgumentException(e);
			}
		}
		return streamToReturn;
	}

}
