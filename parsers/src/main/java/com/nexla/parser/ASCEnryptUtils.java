package com.nexla.parser;

import com.nexla.common.io.CloseableInputStream;
import com.nexla.connector.config.FileEncryptConfig;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.nio.file.Files;

public class ASCEnryptUtils {

	private static final String PGP_EXTENSION = ".asc";

	public static EncryptEngine getEncryptionEngine(FileEncryptConfig config) {

		switch (config.encryptStandard.get()) {
			case PGP:
				EncryptEngine engine = new PGPEngine();
				// Handle optional public key for decryption-only mode
				String publicKey = config.externalPublicKey.orElse("");
				engine.init(config.privateKey.get(), publicKey, config.privatePassword.get(), config.encryptUserId.get(), config.externalUserId.get());

				((PGPEngine) engine)
					.initHashAlg(config.dataHashAlgorithm)
					.initEncryptAlg(config.dataEncryptionAlgorithm)
					.initCompressionAlg(config.dataCompressionAlgorithm);

				return engine;
			default:
				throw new IllegalArgumentException("No such encryption algorithm");
		}
	}

	@SneakyThrows
	public static String encrypt(String outputPath, FileEncryptConfig fileEncryptConfig) {

		EncryptEngine encryptEngine = getEncryptionEngine(fileEncryptConfig);
		String encryptedFileName = outputPath + PGP_EXTENSION;
		try (
			ByteArrayOutputStream encryptedStream = encryptEngine.encrypt(new FileInputStream(outputPath));
			OutputStream outputStream = new FileOutputStream(encryptedFileName)
		) {
			encryptedStream.writeTo(outputStream);
		}
		return encryptedFileName;
	}

	@SneakyThrows
	public static InputStream decrypt(InputStream inputStream, FileEncryptConfig fileEncryptConfig) {
		EncryptEngine encryptEngine = getEncryptionEngine(fileEncryptConfig);
		InputStream in = encryptEngine.readEncrypted(inputStream);

		File tempEncFile = Files.createTempFile("", "").toFile();
		try (FileOutputStream fileWriter = new FileOutputStream(tempEncFile)) {
			IOUtils.copyLarge(in, fileWriter);
		}
		return new CloseableInputStream(new FileInputStream(tempEncFile)).onClose(() -> tempEncFile.delete());
	}

	/**
	 * Decrypts the input file and replaces it
	 */
	@SneakyThrows
	public static void decrypt(File inputFile, FileEncryptConfig fileEncryptConfig) {
		EncryptEngine encryptEngine = getEncryptionEngine(fileEncryptConfig);
		File tempFile = Files.createTempFile("", "").toFile();

		// Step 1. Decrypt file
		try (BufferedInputStream inputStream = new BufferedInputStream(Files.newInputStream(inputFile.toPath()));
			 InputStream decryptedInputStream = encryptEngine.readEncrypted(inputStream);
			 FileOutputStream fileWriter = new FileOutputStream(tempFile)) {
			IOUtils.copyLarge(decryptedInputStream, fileWriter);
		}
		// Step 2. Replace the origin file
		if (!inputFile.delete() || !tempFile.renameTo(inputFile)) {
			throw new IOException("Failed to persist encrypted file");
		}
	}
}
