[{"nexla_op_url": "url", "nexla_op_object": "dataset", "nexla_op_record": {"id": 8, "int_string": "test insert", "float_string": "float string has csv delimiter; lets see if it gets escaped", "string_float": "2", "boolean_string": "boolean string has //\\ and 'single quotes'", "new_int_column": "1"}, "nexla_op_primary_keys": [], "nexla_op_metadata": {"overridden_mappings": {"person": {"partial_enabled": true, "name": "person_overridden_mapping_partial", "columns": {"id": {"name": "ID_Person", "type": "VARCHAR(3000)"}, "name": {"name": "Name_Person", "type": "NUMBER"}}}}, "overridden_db_type_mappings": {"tables": {"person": {"NULL": "VARCHAR(5000)"}}, "columns": {"id": "NUMBER(12,10)", "name": "BINARY"}, "general": {"NULL": "VARCHAR(3000)", "INTEGER": "INT64", "FLOAT": "FLOAT64", "JSON": "JSON", "BOOLEAN": "BOOLEAN", "STRING": "STRING"}}}}, {"nexla_op_url": "url", "nexla_op_object": "dataset", "nexla_op_record": {"id": 8, "int_string": ";d;f;f;f;f;f;f;", "float_string": "\\g\\g\\g\\g\\g\\", "string_float": ";;;f;f;f;f", "boolean_string": "true", "new_int_column": "1"}, "nexla_op_primary_keys": [""]}, {"nexla_op_url": "url", "nexla_op_object": "dataset", "nexla_op_record": {"id": 8, "int_string": "same id insert 2", "float_string": "same id insert 2", "string_float": "2", "boolean_string": "true", "new_int_column": "1"}, "nexla_op_primary_keys": ["id"]}, {"nexla_op_url": "url", "nexla_op_object": "dataset", "nexla_op_record": {"id": 8, "timestamp_tz": "2025-12-22T22:21:49.123+0300", "timestamp_tz_2": "2025-12-22T22:21:49.123-10:00", "timestamp_tz_3": "2025-12-22T22:21:49.123+0000", "timestamp_tz_4": "2025-12-22 22:21:49.123Z", "timestamp": "2023-12-22T22:21:49", "timestamp_ntz": "2024-12-22 22:21:49.123", "date_field": "2025-04-22", "timestamp_null": null}, "nexla_op_primary_keys": ["id"]}]