package com.nexla.connector.sql.processor.elt;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.MappingConfig;
import com.nexla.transform.schema.FormatDetector;
import connect.data.*;
import connect.jdbc.sink.dialect.DbDialect;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.Date;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class JdbcELTSchemaMappingResolver {

  // This mapping is used to convert Nexla message raw converted types to schema type
  private static final Map<Class<?>, Schema.Type> SCHEMA_TYPE_MAPPING = ImmutableMap.<Class<?>, Schema.Type>builder()
      .put(String.class, Schema.Type.STRING)
      .put(Date.class, Schema.Type.STRING)
      .put(Map.class, Schema.Type.JSON)
      .put(List.class, Schema.Type.JSON)
      .put(Integer.class, Schema.Type.INT64)
      .put(Long.class, Schema.Type.INT64)
      .put(BigInteger.class, Schema.Type.INT64)
      .put(Float.class, Schema.Type.FLOAT64)
      .put(Double.class, Schema.Type.FLOAT64)
      .put(BigDecimal.class, Schema.Type.FLOAT64)
      .put(Boolean.class, Schema.Type.BOOLEAN)
      .build();

  // This mapping determines the precedence of the types detected. For example if in the same batch an attribute is a float and a string,
  // we need to pick string as its type
  private static final Map<Class<?>, Integer> TYPE_PRECEDENCE_MAPPING = ImmutableMap.<Class<?>, Integer>builder()
      .put(Map.class, 4)
      .put(List.class, 4)
      .put(String.class, 3)
      .put(Date.class, 3)
      .put(Float.class, 2)
      .put(Double.class, 2)
      .put(BigDecimal.class, 2)
      .put(BigInteger.class, 1)
      .put(Integer.class, 1)
      .put(Long.class, 1)
      .put(Boolean.class, 0)
      .build();

  // This mapping is used to convert schema type to mapping config overridden data type
  private static final Map<Schema.Type, MappingConfig.DataType> SCHEMA_DATA_TYPE_MAPPING = Map.of(
      Schema.Type.STRING, MappingConfig.DataType.STRING,
      Schema.Type.INT32, MappingConfig.DataType.INTEGER,
      Schema.Type.INT64, MappingConfig.DataType.INTEGER,
      Schema.Type.FLOAT64, MappingConfig.DataType.FLOAT,
      Schema.Type.BOOLEAN, MappingConfig.DataType.BOOLEAN,
      Schema.Type.JSON, MappingConfig.DataType.JSON,
      Schema.Type.UNSUPPORTED, MappingConfig.DataType.NULL);

  // For some data types, we need to convert it to a type that the db dialect will understand, this is an extra step to support overridden data types
  private static final Map<Schema.Type, Schema.Type> LOGICAL_SCHEMA_TYPE_MAPPING = Map.of(Schema.Type.JSON, Schema.Type.STRING);

  private final Optional<MappingConfig.OverriddenDbTypeMapping> optionalOverriddenDbTypeMapping;

  private final LinkedHashMap<String, MappingConfig.TableMapping> overriddenMappings;

  private final DbDialect dbDialect;

  private final NexlaLogger logger;

  public JdbcELTSchemaMappingResolver(MappingConfig sinkMappingConfig, Optional<MappingConfig> metadataMappingConfig, DbDialect dbDialect, NexlaLogger logger) {
    this.logger = logger;
    this.dbDialect = dbDialect;
    this.overriddenMappings = consolidateOverriddenMappings(sinkMappingConfig.getOverriddenMappings(),
        metadataMappingConfig.map(MappingConfig::getOverriddenMappings).orElse(Maps.newLinkedHashMap()));
    this.optionalOverriddenDbTypeMapping = Optional.ofNullable(sinkMappingConfig.getOverriddenDbTypeMapping())
        .or(() -> metadataMappingConfig.map(MappingConfig::getOverriddenDbTypeMapping));
  }

  private LinkedHashMap<String, MappingConfig.TableMapping> consolidateOverriddenMappings(
      LinkedHashMap<String, MappingConfig.TableMapping> sinkOverriddenMapping,
      LinkedHashMap<String, MappingConfig.TableMapping> metadataOverriddenMapping) {

    // sink mapping has higher precedence than metadata mapping
    LinkedHashMap<String, MappingConfig.TableMapping> mergedMap = Maps.newLinkedHashMap(sinkOverriddenMapping);

    metadataOverriddenMapping.forEach((tableName, value) -> {
      if(!mergedMap.containsKey(tableName)) {
        mergedMap.put(tableName, value);
      }
    });
    return mergedMap;
  }

  public Schema detectSchema(List<NexlaMessageContext> messages, String tableName) {
    // group by field name and list of values type
    LinkedHashMap<String, Object> distinctKeyValues = StreamEx.of(messages)
        .map(NexlaMessageContext::getMapped)
        .map(NexlaMessage::getRawMessage)
        .flatMap(map -> map.entrySet().stream())
        .filter(entry -> isNullSupported(entry.getKey(), tableName) || Objects.nonNull(entry.getValue()))
        .collect(LinkedHashMap::new, (currentMap,entry)-> {
          Object oldValue = currentMap.get(entry.getKey());
          currentMap.put(entry.getKey(), determineHighestPrecedenceType(oldValue, entry.getValue()));
        }, HashMap::putAll);

    SchemaBuilder schemaBuilder = SchemaBuilder.struct().name(tableName);

    // for each key value detected, create schema type
    distinctKeyValues.forEach((fieldName, value) -> {
      Optional<Schema> optionalDateSchema = checkIfIsTimestampOrDateType(value);
      if (optionalDateSchema.isPresent()) {
        schemaBuilder.field(fieldName, optionalDateSchema.get());
      } else {
        Schema.Type schemaType = getSchemaType(value);
        Schema fieldSchema = SchemaBuilder.type(schemaType).build();
        schemaBuilder.field(fieldName, fieldSchema);
      }
    });

    ConnectSchema schema = schemaBuilder.build();

    logger.info("M=detectSchemaFromRawData, schemaFields={}", schema.fields());
    return schema;
  }

  public LinkedHashMap<String, Map<String, String>> resolveMappingConfig(String tableName, Schema schema) {
    Optional<MappingConfig.TableMapping> overriddenMapping = Optional.ofNullable(overriddenMappings.get(tableName));

    return overriddenMapping
        .filter(it -> !it.isPartialEnabled())
        .map(this::buildMappingUsingOverriddenMapping)
        .orElse(buildMappingUsingSchema(schema, tableName, overriddenMapping));
  }

  private LinkedHashMap<String, Map<String, String>> buildMappingUsingSchema(Schema schema, String tableName, Optional<MappingConfig.TableMapping> overriddenMapping) {
    return schema.fields()
        .stream()
        .map(it -> getMappingForField(it, tableName, overriddenMapping))
        .flatMap(map -> map.entrySet().stream())
        .collect(Collectors.toMap(
            Map.Entry::getKey,
            Map.Entry::getValue,
            (oldValue, newValue) -> {
              oldValue.putAll(newValue);
              return oldValue;
            },
            LinkedHashMap::new
        ));
  }

  private LinkedHashMap<String, Map<String, String>> buildMappingUsingOverriddenMapping(MappingConfig.TableMapping overriddenMapping) {
    return overriddenMapping
        .getColumns()
        .entrySet()
        .stream()
        .map(entry -> {
          String targetColName = dbDialect.getTargetName(entry.getValue().getName());
          return Map.of(entry.getKey(), Collections.singletonMap(targetColName, entry.getValue().getType()));
        })
        .flatMap(map -> map.entrySet().stream())
        .collect(Collectors.toMap(
            Map.Entry::getKey,
            Map.Entry::getValue,
            (oldValue, newValue) -> {
              oldValue.putAll(newValue);
              return oldValue;
            },
            LinkedHashMap::new
        ));
  }

  private Map<String, Map<String, String>> getMappingForField(Field field, String tableName, Optional<MappingConfig.TableMapping> overriddenMapping) {
    Optional<MappingConfig.ColumnMapping> optionalColumnMapping = overriddenMapping.map(it -> it.getColumns().get(field.name()));
    String mappingName = optionalColumnMapping.map(MappingConfig.ColumnMapping::getName).orElse(field.name());
    String sqlType = optionalColumnMapping.map(MappingConfig.ColumnMapping::getType).orElse(getSqlType(field, tableName));
    String targetFieldName = dbDialect.getTargetName(mappingName);
    return Collections.singletonMap(field.name(), Collections.singletonMap(targetFieldName, sqlType));
  }

  // add the ability of identifying our type (ENUM) or what the user specifies
  private String getSqlType(Field field, String tableName) {
    Schema.Type schemaType = field.schema().type();
    String schemaName = field.schema().name();
    return determineOverriddenDbTypeMapping(field.name(), schemaName, tableName, SCHEMA_DATA_TYPE_MAPPING.get(schemaType))
        .filter(StringUtils::isNotBlank)
        .map(this::maybeProcessAsSchemaType)
        .orElse(getSqlType(schemaType, schemaName));
  }

  private String maybeProcessAsSchemaType(String overriddenDbTypeMapping) {
    boolean isNexlaType = Stream.of(Schema.Type.values())
        .map(Schema.Type::name)
        .collect(Collectors.toList())
        .contains(overriddenDbTypeMapping.toUpperCase());

    if (isNexlaType) {
      var schemaType = Schema.Type.valueOf(overriddenDbTypeMapping.toUpperCase());
      return getSqlType(schemaType, null);
    }

    return overriddenDbTypeMapping;
  }

  private String getSqlType(Schema.Type schemaType, String schemaName) {
    return dbDialect.getSqlType(schemaName, Map.of(), LOGICAL_SCHEMA_TYPE_MAPPING.getOrDefault(schemaType, schemaType));
  }

  // if you see data type X in table Y map it to Z type
  // if you see any column named X, map it to Z type
  // for all data types detected, map the columns to Z data type
  // for any table and column when you detect data type X, map it to Y data type
  // precedence is table > column > all > general
  private Optional<String> determineOverriddenDbTypeMapping(String columnName, String fieldSchemaName, String tableName, MappingConfig.DataType dataType) {
    if (optionalOverriddenDbTypeMapping.isPresent() && StringUtils.isBlank(fieldSchemaName)) {
      MappingConfig.OverriddenDbTypeMapping overriddenDbTypeMapping = optionalOverriddenDbTypeMapping.get();

      Optional<String> allDataTypes = Optional.ofNullable(overriddenDbTypeMapping.getAllDataTypes()).filter(StringUtils::isNotBlank);

      Optional<String> overrideByColumnName = Optional.ofNullable(overriddenDbTypeMapping.getColumns().get(columnName));

      Optional<String> overrideByTableNameAndColumnType = Optional.ofNullable(overriddenDbTypeMapping.getTables().get(tableName))
          .map(it -> it.get(dataType));

      Optional<String> generalOverrideByColumnType = Optional.ofNullable(overriddenDbTypeMapping.getGeneral().get(dataType));

      if (overrideByTableNameAndColumnType.isPresent()) {
        return overrideByTableNameAndColumnType;
      }

      if (overrideByColumnName.isPresent()) {
        return overrideByColumnName;
      }

      if (allDataTypes.isPresent()) {
        return allDataTypes;
      }

      if (generalOverrideByColumnType.isPresent()) {
        return generalOverrideByColumnType;
      }
    }

    return Optional.empty();
  }

  private Object determineHighestPrecedenceType(Object oldValue, Object newValue) {
    if (Objects.isNull(oldValue) || Objects.isNull(newValue)) {
      return ObjectUtils.firstNonNull(oldValue, newValue);
    }
    var newValuePrecedence = findTypePrecedence(newValue);
    var oldValuePrecedence = findTypePrecedence(oldValue);

    if (newValuePrecedence > oldValuePrecedence) {
      logger.info("M=determineHighestPrecedenceType, new value {} has higher precedence than old one {}", newValue, oldValue);
      return newValue;
    }
    return oldValue;
  }

  private Optional<String> getDbTypeForNulls(String columnName, String tableName) {
    return determineOverriddenDbTypeMapping(columnName, null, tableName, MappingConfig.DataType.NULL);
  }

  private boolean isNullSupported(String columnName, String tableName) {
    return getDbTypeForNulls(columnName, tableName).isPresent();
  }

  private Integer findTypePrecedence(Object obj) {
    return TYPE_PRECEDENCE_MAPPING
        .entrySet()
        .stream()
        .filter(entry -> entry.getKey().isInstance(obj))
        .map(Map.Entry::getValue)
        .findFirst()
        .orElseThrow(() -> new RuntimeException("Can't find precedence mapping for class " + obj.getClass()));
  }

  private Schema.Type getSchemaType(Object obj) {
    if (Objects.isNull(obj)) {
      return Schema.Type.UNSUPPORTED; // later this will be translated to the correct data type when building the mapping, just need a way to keep track of it as a schema field
    }

    return SCHEMA_TYPE_MAPPING
        .entrySet()
        .stream()
        .filter(entry -> entry.getKey().isInstance(obj))
        .map(Map.Entry::getValue)
        .findFirst()
        .orElseThrow(() -> new RuntimeException("Can't find mapping for class " + obj.getClass()));
  }

  private Optional<Schema> checkIfIsTimestampOrDateType(Object obj) {
    if (Objects.nonNull(obj) && obj instanceof String) {
      var dateOrTimestamp = FormatDetector.isDateorTimestamp(obj.toString());
      if (FormatDetector.DateTimeTypeEnum.TIMESTAMP == dateOrTimestamp) {
        var normalizedTimestamp = TimestampNormalizer.normalizeTimestamp(obj.toString());
        if (normalizedTimestamp.isHasTimezone()) {
          return Optional.of(TimestampTimezone.SCHEMA);
        }
        return Optional.of(Timestamp.SCHEMA);
      } else if (FormatDetector.DateTimeTypeEnum.DATE == dateOrTimestamp) {
        return Optional.of(connect.data.Date.SCHEMA);
      }
    }
    return Optional.empty();
  }
}
