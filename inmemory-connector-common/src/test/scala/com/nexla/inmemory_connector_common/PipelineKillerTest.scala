package com.nexla.inmemory_connector_common

import org.scalatest.TagAnnotation
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class PipelineKillerTest extends AnyFunSuite with Matchers {


  test("stop should return false when no kill functions are added") {
    val killer = new PipelineKiller
    killer.stop() shouldBe false
  }

  test("stop should return true when one kill function returns true") {
    val killer = new PipelineKiller
    killer.addKillFunction(() => true)
    killer.stop() shouldBe true
  }

  test("stop should return false when one kill function returns false") {
    val killer = new PipelineKiller
    killer.addKillFunction(() => false)
    killer.stop() shouldBe false
  }

  test("stop should return true when all multiple kill functions return true") {
    val killer = new PipelineKiller
    killer.addKillFunction(() => true)
    killer.addKillFunction(() => true)
    killer.addKillFunction(() => true)
    killer.stop() shouldBe true
  }

  test("stop should return false when any kill function returns false") {
    val killer = new PipelineKiller
    killer.addKillFunction(() => true)
    killer.addKillFunction(() => false)
    killer.addKillFunction(() => true)
    killer.stop() shouldBe false
  }

  test("stop should call all kill functions") {
    var callCount = 0
    val killer = new PipelineKiller
    killer.addKillFunction(() => { callCount += 1; true })
    killer.addKillFunction(() => { callCount += 1; true })
    killer.stop()
    callCount shouldBe 2
  }

  }
